<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>FXAA source</Name>
  <Location>/Engine/Shaders/Private/FXAAShader.usf</Location>
  <Date>2016-06-17T14:01:37.5004259-04:00</Date>
  <Function>C++ and shader code to smooth edges in an rendered images in a single postprocess pass</Function>
  <Justification>C++ and shader code to smooth edges in an rendered images in a single postprocess pass</Justification>
  <Eula>Custom agreement between Epic/NVIDIA</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>