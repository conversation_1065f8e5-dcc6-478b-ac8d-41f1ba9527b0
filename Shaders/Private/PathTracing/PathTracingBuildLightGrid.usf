// Copyright Epic Games, Inc. All Rights Reserved.

#include "../Common.ush"
#include "/Engine/Shared/RayTracingDefinitions.h"
#include "/Engine/Shared/RayTracingTypes.h"
#include "/Engine/Shared/PathTracingDefinitions.h"

#define USE_ATTENUATION_TERM 0
#define USE_IES_TERM 0
#include "Light/PathTracingLightCommon.ush"

uint SceneInfiniteLightCount;
float3 SceneLightsTranslatedBoundMin;
float3 SceneLightsTranslatedBoundMax;
RWTexture2D<uint> RWLightGrid;
RWBuffer<uint> RWLightGridData;
uint LightGridResolution;
uint LightGridMaxCount;
int LightGridAxis;


[numthreads(THREADGROUPSIZE_X, THREADGROUPSIZE_Y, 1)]
void PathTracingBuildLightGridCS(uint2 DispatchThreadId : SV_DispatchThreadID)
{
	if (any(DispatchThreadId >= LightGridResolution))
	{
		return;
	}
	// figure out dimension of the 3d grid and the current ID to be filled
	uint3 VoxelId = 0, VoxelRes = 1;
	switch (LightGridAxis)
	{
		case 0: VoxelId.yz = DispatchThreadId; VoxelRes.yz = LightGridResolution; break;
		case 1: VoxelId.xz = DispatchThreadId; VoxelRes.xz = LightGridResolution; break;
		case 2: VoxelId.xy = DispatchThreadId; VoxelRes.xy = LightGridResolution; break;
	}
	// get bounding box of current voxel
	float3 Lo = lerp(SceneLightsTranslatedBoundMin, SceneLightsTranslatedBoundMax, float3(VoxelId + 0) / float3(VoxelRes));
	float3 Hi = lerp(SceneLightsTranslatedBoundMin, SceneLightsTranslatedBoundMax, float3(VoxelId + 1) / float3(VoxelRes));

	uint NumVisible = 0;
	uint MaxVisible = min(LightGridMaxCount, RAY_TRACING_LIGHT_COUNT_MAXIMUM - SceneInfiniteLightCount);
	uint Offset = LightGridMaxCount * (DispatchThreadId.x + LightGridResolution * DispatchThreadId.y);
	bool AllSingular = true;
	for (uint LightIndex = SceneInfiniteLightCount; LightIndex < SceneLightCount; LightIndex++)
	{
		uint LightType = SceneLights[LightIndex].Flags & PATHTRACER_FLAG_TYPE_MASK;
		float3 Center = GetTranslatedPosition(LightIndex);
		float Radius = 1.0 / GetAttenuation(LightIndex);
		float3 Normal = GetNormal(LightIndex);

		float3 E = max(Lo - Center, 0) + max(Center - Hi, 0);
		float D2 = dot(E, E);

		// reject against normal
		if (LightType != PATHTRACING_LIGHT_POINT)
		{
			if (dot(select(Normal > 0, Hi, Lo) - Center, Normal) < 0)
			{
				continue;
			}
		}

		if (LightType == PATHTRACING_LIGHT_SPOT)
		{
			float3 Disc = sqrt(saturate(1.0 - Normal * Normal));
			// box around ray from light center to tip of the cone
			float3 Tip = Center + Normal * Radius;
			float3 LightBoundLo = min(Center, Tip);
			float3 LightBoundHi = max(Center, Tip);
			// expand by disc around the farthest part of the cone

			float CosOuter = GetCosConeAngles(LightIndex).x;
			float SinOuter = sqrt(1.0f - CosOuter * CosOuter);

			LightBoundLo = min(LightBoundLo, Center + Radius * (Normal * CosOuter - Disc * SinOuter));
			LightBoundHi = max(LightBoundHi, Center + Radius * (Normal * CosOuter + Disc * SinOuter));

			if (any(Hi < LightBoundLo) || any(Lo > LightBoundHi))
			{
				continue;
			}
		}

		if (D2 < Square(Radius))
		{
			AllSingular = AllSingular && all(SceneLights[LightIndex].Dimensions == 0);
			RWLightGridData[Offset + NumVisible] = LightIndex;
			NumVisible++;
			// TODO: handle overflow better?
			if (NumVisible >= MaxVisible)
			{
				break;
			}
		}
	}
	if (AllSingular)
	{
		NumVisible |= PATHTRACER_LIGHT_GRID_SINGULAR_MASK;
	}
	RWLightGrid[DispatchThreadId] = NumVisible;
}
