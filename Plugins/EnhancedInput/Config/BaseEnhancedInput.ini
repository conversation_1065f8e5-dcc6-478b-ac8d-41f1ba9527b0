[CoreRedirects]
+FunctionRedirects=(OldName="/Script/InputMappingContext.UnmapAction", NewName="/Script/InputMappingContext.UnmapAllKeysFromAction")

; 5.2
+FunctionRedirects=(OldName="/Script/EnhancedInput.EnhancedInputSubsystemInterface.AddPlayerMappedKey", NewName="/Script/EnhancedInput.EnhancedInputSubsystemInterface.K2_AddPlayerMappedKeyInSlot")
+FunctionRedirects=(OldName="/Script/EnhancedInput.EnhancedInputSubsystemInterface.RemovePlayerMappedKey", NewName="/Script/EnhancedInput.EnhancedInputSubsystemInterface.K2_RemovePlayerMappedKeyInSlot")
+FunctionRedirects=(OldName="/Script/EnhancedInput.EnhancedInputSubsystemInterface.GetPlayerMappedKey", NewName="/Script/EnhancedInput.EnhancedInputSubsystemInterface.K2_GetPlayerMappedKeyInSlot")