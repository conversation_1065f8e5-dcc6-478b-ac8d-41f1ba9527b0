#usda 1.0

(
    subLayers = [
        @usdGeom/schema.usda@
    ]
)
 
over "GLOBAL" (
    customData = {
        string libraryName = "usdVol"
        string libraryPath = "pxr/usd/usdVol"
	dictionary libraryTokens = {
            dictionary field = {
                string doc = """This is the namespace prefix used to 
                specify the fields that make up a volume primitive."""
            }
	}
    }
)
{
}

class Volume "Volume" (
    inherits = </Gprim>
    doc = """A renderable volume primitive. A volume is made up of any number
             of FieldBase primitives bound together in this volume. Each
             FieldBase primitive is specified as a relationship with a
             namespace prefix of "field".

             The relationship name is used by the renderer to associate
             individual fields with the named input parameters on the volume
             shader. Using this indirect approach to connecting fields to
             shader parameters (rather than using the field prim's name)
             allows a single field to be reused for different shader inputs, or
             to be used as different shader parameters when rendering different
             Volumes. This means that the name of the field prim is not
             relevant to its contribution to the volume prims which refer to
             it. Nor does the field prim's location in the scene graph have
             any relevance, and Volumes may refer to fields anywhere in the
             scene graph.  **However**, unless Field prims need to be shared
             by multiple Volumes, a Volume's Field prims should be located
             under the Volume in namespace, for enhanced organization."""
)
{
}

class "FieldBase" (
    inherits = </Xformable>
    doc = """Base class for field primitives."""
)
{
}

class "FieldAsset" (
    doc = "Base class for field primitives defined by an external file."
    inherits = </FieldBase>
)
{
    asset filePath (
        doc = """An asset path attribute that points to a file on disk.
                 For each supported file format, a separate FieldAsset
                 subclass is required. 
                  
                 This attribute's value can be animated over time, as most
                 volume asset formats represent just a single timeSample of
                 a volume.  However, it does not, at this time, support
                 any pattern substitutions like \"$F\". """
    )
    token fieldName (
        doc = """Name of an individual field within the file specified by
                 the filePath attribute."""
    )
    int fieldIndex (
        doc = """A file can contain multiple fields with the same
                 name. This optional attribute is an index used to
                 disambiguate between these multiple fields with the same
                 name."""
    )
    token fieldDataType (
        doc = """Token which is used to indicate the data type of an
                 individual field. Authors use this to tell consumers more
                 about the field without opening the file on disk. The list of 
                 allowed tokens is specified with the specific asset type. 
                 A missing value is considered an error."""
    )
    token vectorDataRoleHint = "None" (
        allowedTokens = ["None", "Point", "Normal", "Vector", "Color"]
        doc = """Optional token which is used to indicate the role of a vector
                 valued field. This can drive the data type in which fields
                 are made available in a renderer or whether the vector values 
                 are to be transformed."""
    )
}
 
class Field3DAsset "Field3DAsset" (
    doc = """Field3D field primitive. The FieldAsset filePath attribute must
             specify a file in the Field3D format on disk."""
    inherits = </FieldAsset>
)
{
    token fieldDataType (
        allowedTokens = ["half", "float", "double",
                         "half3", "float3", "double3"]
        doc = """Token which is used to indicate the data type of an
                 individual field. Authors use this to tell consumers more
                 about the field without opening the file on disk. The list of 
                 allowed tokens reflects the available choices for Field3d 
                 volumes."""
    )
    token fieldPurpose (
        doc = """Optional token which can be used to indicate the purpose or 
                 grouping of an individual field. Clients which consume Field3D 
                 files should treat this as the Field3D field \\em name."""
    )
}
 
class OpenVDBAsset "OpenVDBAsset" (
    doc = """OpenVDB field primitive. The FieldAsset filePath attribute must
             specify a file in the OpenVDB format on disk."""
    inherits = </FieldAsset>
)
{
    token fieldDataType (
        allowedTokens = ["half", "float", "double", "int", "uint", "int64",
                         "half2", "float2", "double2", "int2",
                         "half3", "float3", "double3", "int3",
                         "matrix3d", "matrix4d", "quatd",
                         "bool", "mask", "string"]
        doc = """Token which is used to indicate the data type of an
                 individual field. Authors use this to tell consumers more
                 about the field without opening the file on disk. The list of 
                 allowed tokens reflects the available choices for OpenVDB 
                 volumes."""
    )
    token fieldClass (
        allowedTokens = ["levelSet", "fogVolume", "staggered", "unknown"]
        doc = """Optional token which can be used to indicate the class of
                 an individual grid. This is a mapping to openvdb::GridClass
                 where the values are GRID_LEVEL_SET, GRID_FOG_VOLUME, 
                 GRID_STAGGERED, and GRID_UNKNOWN."""
    )
}

