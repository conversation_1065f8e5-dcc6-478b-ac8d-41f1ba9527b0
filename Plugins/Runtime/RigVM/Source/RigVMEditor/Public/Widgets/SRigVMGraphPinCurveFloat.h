// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Widgets/SWidget.h"
#include "SCurveEditor.h"
#include "Curves/CurveFloat.h"
#include "SGraphPin.h"

class RIGVMEDITOR_API SRigVMGraphPinCurveFloat : public SGraphPin, public FCurveOwnerInterface
{
public:
	SLATE_BEGIN_ARGS(SRigVMGraphPinCurveFloat) {}
	SLATE_END_ARGS()

	void Construct(const FArguments& InArgs, UEdGraphPin* InGraphPinObj);

	/** FCurveOwnerInterface interface */
	virtual TArray<FRichCurveEditInfoConst> GetCurves() const override;
	virtual TArray<FRichCurveEditInfo> GetCurves() override;
	virtual void ModifyOwner() override;
	virtual TArray<const UObject*> GetOwners() const override;
	virtual void MakeTransactional() override;
	virtual void OnCurveChanged(const TArray<FRichCurveEditInfo>& ChangedCurveEditInfos) override;
	virtual bool IsValidCurve(FRichCurveEditInfo CurveInfo) override;

protected:

	//~ Begin SGraphPin Interface
	virtual TSharedRef<SWidget>	GetDefaultValueWidget() override;
	virtual void Tick(const FGeometry& AllottedGeometry, const double InCurrentTime, const float InDeltaTime) override;
	//~ End SGraphPin Interface

	FRuntimeFloatCurve& UpdateAndGetCurve();

	TSharedPtr<SCurveEditor> CurveEditor;
	FRuntimeFloatCurve Curve;
};
