{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Geometry Cache", "Description": "Support for distilled Geometry animations", "Category": "Importers", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/en-US/WorkingWithContent/Importing/AlembicImporter/#importasgeometrycache", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Plugins": [{"Name": "Niagara", "Enabled": true}], "Modules": [{"Name": "GeometryCacheEd", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>"]}, {"Name": "GeometryCacheSequencer", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>"]}, {"Name": "GeometryCacheStreamer", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}, {"Name": "GeometryCache", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "GeometryCacheTracks", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}]}