{"FileVersion": 3, "Version": 1, "VersionName": "2024.3.3", "FriendlyName": "RiderLink", "Description": "Plugin for establishing IPC connection with JetBrains Rider IDE", "Category": "Programming", "CreatedBy": "JetBrains", "CreatedByURL": "https://www.jetbrains.com/", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": false, "Installed": false, "Modules": [{"Name": "RD", "Type": "EditorNoCommandlet", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "RiderLink", "Type": "EditorNoCommandlet", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "RiderLogging", "Type": "EditorNoCommandlet", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "RiderBlueprint", "Type": "EditorNoCommandlet", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "RiderGameControl", "Type": "EditorNoCommandlet", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "RiderShaderInfo", "Type": "EditorNoCommandlet", "LoadingPhase": "PostEngineInit"}, {"Name": "RiderLC", "Type": "EditorNoCommandlet", "LoadingPhase": "PostEngineInit"}, {"Name": "RiderDebuggerSupport", "Type": "EditorNoCommandlet", "LoadingPhase": "<PERSON><PERSON>efault", "PlatformAllowList": ["Win64"]}], "EnabledByDefault": true}