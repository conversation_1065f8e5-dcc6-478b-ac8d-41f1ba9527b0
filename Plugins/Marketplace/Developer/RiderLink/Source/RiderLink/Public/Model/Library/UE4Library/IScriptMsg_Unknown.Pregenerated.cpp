//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a RdGen v1.13.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#include "IScriptMsg_Unknown.Pregenerated.h"



#ifdef _MSC_VER
#pragma warning( push )
#pragma warning( disable:4250 )
#pragma warning( disable:4307 )
#pragma warning( disable:4267 )
#pragma warning( disable:4244 )
#pragma warning( disable:4100 )
#endif

namespace JetBrains {
namespace EditorPlugin {
// companion
// constants
// initializer
void IScriptMsg_Unknown::initialize()
{
}
// primary ctor
IScriptMsg_Unknown::IScriptMsg_Unknown(rd::RdId unknownId_, rd::Buffer::ByteArray unknownBytes_) :
IScriptMsg(), rd::IUnknownInstance(std::move(unknownId_))

{
    initialize();
}
// secondary constructor
// default ctors and dtors
IScriptMsg_Unknown::IScriptMsg_Unknown()
{
    initialize();
}
// reader
IScriptMsg_Unknown IScriptMsg_Unknown::read(rd::SerializationCtx& ctx, rd::Buffer & buffer)
{
    throw std::logic_error("Unknown instances should not be read via serializer");
}
// writer
void IScriptMsg_Unknown::write(rd::SerializationCtx& ctx, rd::Buffer& buffer) const
{
    buffer.write_byte_array_raw(unknownBytes_);
}
// virtual init
// identify
// getters
// intern
// equals trait
bool IScriptMsg_Unknown::equals(rd::ISerializable const& object) const
{
    auto const &other = dynamic_cast<IScriptMsg_Unknown const&>(object);
    if (this == &other) return true;
    
    return true;
}
// equality operators
bool operator==(const IScriptMsg_Unknown &lhs, const IScriptMsg_Unknown &rhs) {
    if (lhs.type_name() != rhs.type_name()) return false;
    return lhs.equals(rhs);
}
bool operator!=(const IScriptMsg_Unknown &lhs, const IScriptMsg_Unknown &rhs){
    return !(lhs == rhs);
}
// hash code trait
size_t IScriptMsg_Unknown::hashCode() const noexcept
{
    size_t __r = 0;
    return __r;
}
// type name trait
std::string IScriptMsg_Unknown::type_name() const
{
    return "IScriptMsg_Unknown";
}
// static type name trait
std::string IScriptMsg_Unknown::static_type_name()
{
    return "IScriptMsg_Unknown";
}
// polymorphic to string
std::string IScriptMsg_Unknown::toString() const
{
    std::string res = "IScriptMsg_Unknown\n";
    return res;
}
// external to string
std::string to_string(const IScriptMsg_Unknown & value)
{
    return value.toString();
}
}
}

#ifdef _MSC_VER
#pragma warning( pop )
#endif

