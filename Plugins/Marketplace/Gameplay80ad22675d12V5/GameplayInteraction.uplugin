{"FileVersion": 3, "Version": 1, "VersionName": "1.1", "FriendlyName": "Gameplay Interaction", "Description": "A flexible, network-replicated interaction system framework based on the gameplay ability system, enabling developers to create dynamic gameplay experiences with customizable features.", "Category": "Gameplay", "CreatedBy": "<PERSON><PERSON><PERSON>", "CreatedByURL": "https://github.com/imnazake", "DocsURL": "https://github.com/imnazake/Unify/wiki/Gameplay-Interaction-Plugin", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/product/77d0d5fecc95470b8446ccb2bcd5cc13", "SupportURL": "https://discord.gg/AKavNKeDmd", "EngineVersion": "5.5.0", "CanContainContent": true, "Installed": true, "Modules": [{"Name": "GameplayInteraction", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "GameplayAbilities", "Enabled": true}, {"Name": "ModularGameplay", "Enabled": true}, {"Name": "EnhancedInput", "Enabled": true}, {"Name": "CommonUI", "Enabled": true}]}