#include "Nodes/UI/FlowNode_CreateWidget.h"
#include "FlowLogChannels.h"
#include "Blueprint/UserWidget.h"
#include "Engine/World.h"
#include "Engine/GameViewportClient.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(FlowNode_CreateWidget)

#define LOCTEXT_NAMESPACE "FlowNode_CreateWidget"

UFlowNode_CreateWidget::UFlowNode_CreateWidget(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	Category = TEXT("UI");
	
	// Set up default pins
	InputPins = { FFlowPin(TEXT("In")) };
	OutputPins = { 
		FFlowPin(TEXT("Success")), 
		FFlowPin(TEXT("Failed")) 
	};
	
	// Default values
	ZOrder = 0;
	bRemoveOnCleanup = true;
	PlayerIndex = 0;
	CreatedWidget = nullptr;
	CachedPlayerController = nullptr;

#if WITH_EDITOR
	NodeDisplayStyle = EFlowNodeStyle::Default;
#endif
}

void UFlowNode_CreateWidget::ExecuteInput(const FName& PinName)
{
	if (PinName != TEXT("In"))
	{
		return;
	}

	// Validate widget class
	if (!IsWidgetClassValid())
	{
		LogError(TEXT("Widget class is not valid or not set"));
		TriggerOutput(TEXT("Failed"), true);
		return;
	}

	// Get player controller
	CachedPlayerController = GetPlayerController();
	if (!CachedPlayerController)
	{
		LogError(FString::Printf(TEXT("Could not find player controller for player index %d"), PlayerIndex));
		TriggerOutput(TEXT("Failed"), true);
		return;
	}

	// Create and add widget
	if (CreateAndAddWidget())
	{
		LogNote(FString::Printf(TEXT("Successfully created widget of class %s"), 
			WidgetClass ? *WidgetClass->GetName() : TEXT("Unknown")));
		TriggerOutput(TEXT("Success"), true);
	}
	else
	{
		LogError(TEXT("Failed to create or add widget to viewport"));
		TriggerOutput(TEXT("Failed"), true);
	}
}

void UFlowNode_CreateWidget::Cleanup()
{
	// Remove widget if configured to do so
	if (bRemoveOnCleanup)
	{
		RemoveWidget();
	}

	// Clear cached references
	CachedPlayerController = nullptr;

	Super::Cleanup();
}

void UFlowNode_CreateWidget::RemoveWidget()
{
	if (CreatedWidget && IsValid(CreatedWidget))
	{
		CreatedWidget->RemoveFromParent();
		LogNote(TEXT("Removed widget from viewport"));
	}
	
	CreatedWidget = nullptr;
}

APlayerController* UFlowNode_CreateWidget::GetPlayerController() const
{
	if (UWorld* World = GetWorld())
	{
		return UGameplayStatics::GetPlayerController(World, PlayerIndex);
	}
	
	return nullptr;
}

bool UFlowNode_CreateWidget::CreateAndAddWidget()
{
	if (!WidgetClass || !CachedPlayerController)
	{
		return false;
	}

	// Remove any existing widget first
	RemoveWidget();

	// Create the widget
	CreatedWidget = CreateWidget<UUserWidget>(CachedPlayerController, WidgetClass);
	if (!CreatedWidget)
	{
		LogError(TEXT("Failed to create widget instance"));
		return false;
	}

	// Add to viewport
	CreatedWidget->AddToViewport(ZOrder);
	
	return true;
}

bool UFlowNode_CreateWidget::IsWidgetClassValid() const
{
	return WidgetClass != nullptr && WidgetClass->IsChildOf(UUserWidget::StaticClass());
}

#if WITH_EDITOR
void UFlowNode_CreateWidget::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property)
	{
		const FName PropertyName = PropertyChangedEvent.Property->GetFName();
		
		// Validate widget class when it changes
		if (PropertyName == GET_MEMBER_NAME_CHECKED(UFlowNode_CreateWidget, WidgetClass))
		{
			if (WidgetClass && !WidgetClass->IsChildOf(UUserWidget::StaticClass()))
			{
				LogError(TEXT("Widget class must be a subclass of UUserWidget"));
				WidgetClass = nullptr;
			}
		}
	}
}

FText UFlowNode_CreateWidget::GetNodeTitle() const
{
	if (WidgetClass)
	{
		return FText::Format(LOCTEXT("NodeTitleWithClass", "Create Widget: {0}"), 
			FText::FromString(WidgetClass->GetName()));
	}
	
	return LOCTEXT("NodeTitle", "Create Widget");
}

FText UFlowNode_CreateWidget::GetNodeToolTip() const
{
	return LOCTEXT("NodeTooltip", "Creates a UserWidget instance and adds it to the player's viewport. The widget class must be specified.");
}

EDataValidationResult UFlowNode_CreateWidget::ValidateNode()
{
	EDataValidationResult Result = Super::ValidateNode();
	
	if (!IsWidgetClassValid())
	{
		LogError(TEXT("Widget class is not set or is not a valid UUserWidget subclass"));
		Result = EDataValidationResult::Invalid;
	}
	
	if (PlayerIndex < 0)
	{
		LogError(TEXT("Player index must be 0 or greater"));
		Result = EDataValidationResult::Invalid;
	}
	
	return Result;
}
#endif

#undef LOCTEXT_NAMESPACE
