// Copyright https://github.com/MothCocoon/FlowGraph/graphs/contributors

#include "FlowTags.h"

UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::CategoryName, "Flow.NodeStyle");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::Custom, "Flow.NodeStyle.Custom");

UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::Node, "Flow.NodeStyle.Node");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::Default, "Flow.NodeStyle.Node.Default");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::Condition, "Flow.NodeStyle.Node.Condition");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::Deprecated, "Flow.NodeStyle.Node.Deprecated");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::<PERSON><PERSON><PERSON>, "Flow.NodeStyle.Node.Developer");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::InOut, "Flow.NodeStyle.Node.InOut");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::Latent, "Flow.NodeStyle.Node.Latent");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::Logic, "Flow.NodeStyle.Node.Logic");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::SubGraph, "Flow.NodeStyle.Node.SubGraph");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::Terminal, "Flow.NodeStyle.Node.Terminal");

UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::AddOn, "Flow.NodeStyle.AddOn");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::AddOn_PerSpawnedActor, "Flow.NodeStyle.AddOn.PerSpawnedActor");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::AddOn_Predicate, "Flow.NodeStyle.AddOn.Predicate");
UE_DEFINE_GAMEPLAY_TAG(FlowNodeStyle::AddOn_Predicate_Composite, "Flow.NodeStyle.AddOn.Predicate.Composite");
