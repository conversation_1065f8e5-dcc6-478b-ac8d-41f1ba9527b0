<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Distributed shaders compilation with FASTBuild</Name>
  <Location>/Engine/Source/Runtime/Engine/Private/ShaderCompiler</Location>
  <Function>Engine modifications to use FASTBuild for distributed shaders compilation on Windows and Linux</Function>
  <Eula>Custom License</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>