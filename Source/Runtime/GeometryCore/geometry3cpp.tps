<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>geometry3cpp</Name>
  <Location>/Engine/Plugins/Experimental/GeometryProcessing</Location>
  <Function>2D/3D geometry processing and geometric modeling.</Function>
  <Eula>https://github.com/gradientspace/geometry3cpp/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/geometry3cpp_License.txt</LicenseFolder>
</TpsData>