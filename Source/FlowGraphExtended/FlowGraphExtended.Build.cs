using UnrealBuildTool;

public class FlowGraphExtended : ModuleRules
{
	public FlowGraphExtended(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"CoreUObject",
				"Engine",
				"Flow", // Core FlowGraph functionality
			}
		);

		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"GameplayTags",
				"Slate",
				"SlateCore",
				"UMG",
				"DeveloperSettings",
				"NetCore"
			}
		);

		// Add editor dependencies when building for editor
		if (Target.Type == TargetType.Editor)
		{
			PrivateDependencyModuleNames.AddRange(
				new string[]
				{
					"FlowEditor", // For editor-time FlowGraph functionality
					"UnrealEd",
					"MessageLog"
				}
			);
		}
	}
}
