#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleInterface.h"
#include "Modules/ModuleManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogFlowGraphExtended, Log, All);

/**
 * FlowGraphExtended Module
 * 
 * This module provides extended FlowGraph functionality including:
 * - Custom FlowNode implementations for visual novel specific features
 * - Extended FlowGraph systems and utilities
 * - Integration helpers for FlowGraph with other game systems
 */
class FLOWGRAPHEXTENDED_API FFlowGraphExtendedModule : public IModuleInterface
{
public:
	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

	/**
	 * Singleton-like access to this module's interface. This is just for convenience!
	 * Beware of calling this during the shutdown phase, though. Your module might have been unloaded already.
	 *
	 * @return Returns singleton instance, loading the module on demand if needed
	 */
	static inline FFlowGraphExtendedModule& Get()
	{
		return FModuleManager::LoadModuleChecked<FFlowGraphExtendedModule>("FlowGraphExtended");
	}

	/**
	 * Checks to see if this module is loaded and ready. It is only valid to call Get() if IsAvailable() returns true.
	 *
	 * @return True if the module is loaded and ready to use
	 */
	static inline bool IsAvailable()
	{
		return FModuleManager::Get().IsModuleLoaded("FlowGraphExtended");
	}
};
