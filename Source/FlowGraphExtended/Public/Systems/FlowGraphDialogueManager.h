#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "GameplayTagContainer.h"
#include "Nodes/FlowNode_DialogueChoice.h"
#include "FlowGraphDialogueManager.generated.h"

class UFlowAsset;
class UFlowNode_DialogueChoice;

/**
 * Dialogue State Information
 */
USTRUCT(BlueprintType)
struct FLOWGRAPHEXTENDED_API FDialogueState
{
	GENERATED_BODY()

	/** Current dialogue flow asset being executed */
	UPROPERTY(BlueprintReadOnly, Category = "Dialogue")
	TObjectPtr<UFlowAsset> CurrentDialogueFlow;

	/** Current dialogue choice node (if any) */
	UPROPERTY(BlueprintReadOnly, Category = "Dialogue")
	TObjectPtr<UFlowNode_DialogueChoice> CurrentChoiceNode;

	/** Player's current gameplay tags affecting dialogue choices */
	UPROPERTY(BlueprintReadWrite, Category = "Dialogue")
	FGameplayTagContainer PlayerTags;

	/** Whether a dialogue is currently active */
	UPROPERTY(BlueprintReadOnly, Category = "Dialogue")
	bool bIsDialogueActive = false;

	FDialogueState()
	{
		CurrentDialogueFlow = nullptr;
		CurrentChoiceNode = nullptr;
		bIsDialogueActive = false;
	}
};

/**
 * Delegate for dialogue events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDialogueChoicesPresented, const TArray<FDialogueChoiceData>&, Choices);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDialogueChoiceSelected, int32, ChoiceIndex);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnDialogueStarted);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnDialogueEnded);

/**
 * FlowGraph Dialogue Manager Subsystem
 * 
 * This subsystem manages dialogue flows and provides integration between
 * FlowGraph dialogue nodes and the game's UI and state systems.
 */
UCLASS(BlueprintType, Blueprintable)
class FLOWGRAPHEXTENDED_API UFlowGraphDialogueManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	/** Get the current dialogue state */
	UFUNCTION(BlueprintPure, Category = "Dialogue")
	const FDialogueState& GetDialogueState() const { return CurrentDialogueState; }

	/** Start a dialogue flow */
	UFUNCTION(BlueprintCallable, Category = "Dialogue")
	bool StartDialogue(UFlowAsset* DialogueFlow);

	/** End the current dialogue */
	UFUNCTION(BlueprintCallable, Category = "Dialogue")
	void EndDialogue();

	/** Present choices to the player (called by FlowNode_DialogueChoice) */
	UFUNCTION(BlueprintCallable, Category = "Dialogue")
	void PresentChoices(UFlowNode_DialogueChoice* ChoiceNode, const TArray<FDialogueChoiceData>& Choices);

	/** Select a dialogue choice (called by UI) */
	UFUNCTION(BlueprintCallable, Category = "Dialogue")
	void SelectChoice(int32 ChoiceIndex);

	/** Add gameplay tags to the player's tag container */
	UFUNCTION(BlueprintCallable, Category = "Dialogue")
	void AddPlayerTags(const FGameplayTagContainer& TagsToAdd);

	/** Remove gameplay tags from the player's tag container */
	UFUNCTION(BlueprintCallable, Category = "Dialogue")
	void RemovePlayerTags(const FGameplayTagContainer& TagsToRemove);

	/** Check if the player has specific tags */
	UFUNCTION(BlueprintPure, Category = "Dialogue")
	bool HasPlayerTags(const FGameplayTagContainer& TagsToCheck) const;

	/** Get all player tags */
	UFUNCTION(BlueprintPure, Category = "Dialogue")
	const FGameplayTagContainer& GetPlayerTags() const { return CurrentDialogueState.PlayerTags; }

public:
	/** Event fired when dialogue choices are presented to the player */
	UPROPERTY(BlueprintAssignable, Category = "Dialogue Events")
	FOnDialogueChoicesPresented OnDialogueChoicesPresented;

	/** Event fired when a dialogue choice is selected */
	UPROPERTY(BlueprintAssignable, Category = "Dialogue Events")
	FOnDialogueChoiceSelected OnDialogueChoiceSelected;

	/** Event fired when dialogue starts */
	UPROPERTY(BlueprintAssignable, Category = "Dialogue Events")
	FOnDialogueStarted OnDialogueStarted;

	/** Event fired when dialogue ends */
	UPROPERTY(BlueprintAssignable, Category = "Dialogue Events")
	FOnDialogueEnded OnDialogueEnded;

protected:
	/** Current dialogue state */
	UPROPERTY(BlueprintReadOnly, Category = "Dialogue")
	FDialogueState CurrentDialogueState;

	/** Cached choices from the current choice node */
	UPROPERTY(Transient)
	TArray<FDialogueChoiceData> CachedChoices;

private:
	/** Internal cleanup when dialogue ends */
	void CleanupDialogue();
};
