#pragma once

#include "CoreMinimal.h"
#include "Nodes/FlowNode_VisualNovelBase.h"
#include "GameplayTagContainer.h"
#include "FlowNode_DialogueChoice.generated.h"

/**
 * Dialogue Choice Data Structure
 * Represents a single choice option in a dialogue system
 */
USTRUCT(BlueprintType)
struct FLOWGRAPHEXTENDED_API FDialogueChoiceData
{
	GENERATED_BODY()

	/** The text displayed for this choice */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Choice")
	FText ChoiceText;

	/** Optional condition tags that must be met for this choice to be available */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Choice")
	FGameplayTagContainer RequiredTags;

	/** Tags that will be added when this choice is selected */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Choice")
	FGameplayTagContainer TagsToAdd;

	/** Whether this choice is currently available */
	UPROPERTY(BlueprintReadOnly, Category = "Choice")
	bool bIsAvailable = true;

	FDialogueChoiceData()
	{
		ChoiceText = FText::GetEmpty();
		bIsAvailable = true;
	}
};

/**
 * Extended FlowNode for handling dialogue choices in visual novels
 * 
 * This node presents multiple dialogue choices to the player and branches
 * execution based on the selected choice. It integrates with gameplay tags
 * for conditional choice availability.
 */
UCLASS(NotBlueprintable, meta = (DisplayName = "Dialogue Choice", Keywords = "dialogue, choice, branch, visual novel"))
class FLOWGRAPHEXTENDED_API UFlowNode_DialogueChoice : public UFlowNode_VisualNovelBase
{
	GENERATED_UCLASS_BODY()

public:
	/** Array of dialogue choices available to the player */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dialogue")
	TArray<FDialogueChoiceData> Choices;

	/** Maximum time allowed for player to make a choice (0 = no time limit) */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dialogue", meta = (ClampMin = "0.0"))
	float ChoiceTimeLimit = 0.0f;

	/** Index of choice to auto-select if time limit expires (-1 = no auto-select) */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dialogue", meta = (ClampMin = "-1"))
	int32 DefaultChoiceIndex = -1;

protected:
	// UFlowNode interface
	virtual void ExecuteInput(const FName& PinName) override;
	virtual void Cleanup() override;

#if WITH_EDITOR
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
	virtual FText GetNodeTitle() const override;
	virtual FText GetNodeToolTip() const override;
#endif

public:
	/** Called when a choice is made by the player */
	UFUNCTION(BlueprintCallable, Category = "Dialogue")
	void OnChoiceSelected(int32 ChoiceIndex);

	/** Get all currently available choices (filtered by required tags) */
	UFUNCTION(BlueprintPure, Category = "Dialogue")
	TArray<FDialogueChoiceData> GetAvailableChoices() const;

	/** Check if a specific choice is available based on required tags */
	UFUNCTION(BlueprintPure, Category = "Dialogue")
	bool IsChoiceAvailable(int32 ChoiceIndex) const;

protected:
	/** Updates the availability of all choices based on current gameplay tags */
	void UpdateChoiceAvailability();

	/** Handles the timeout for choice selection */
	void OnChoiceTimeout();

	/** Timer handle for choice timeout */
	FTimerHandle ChoiceTimeoutHandle;

	/** Cached reference to gameplay tag subsystem for tag checking */
	UPROPERTY(Transient)
	TObjectPtr<class UGameplayTagsManager> GameplayTagsManager;

private:
	/** Internal flag to prevent multiple choice selections */
	bool bChoiceSelected = false;
};
