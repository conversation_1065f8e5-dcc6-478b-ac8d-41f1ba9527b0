#pragma once

#include "CoreMinimal.h"
#include "Nodes/FlowNode.h"
#include "Blueprint/UserWidget.h"
#include "Components/Widget.h"
#include "FlowNode_CreateWidget.generated.h"

/**
 * FlowNode that creates a UserWidget and attaches it to the player's screen
 * 
 * This node takes a UserWidget class as input and creates an instance of it,
 * then adds it to the viewport for the specified player.
 */
UCLASS(NotBlueprintable, meta = (DisplayName = "Create Widget", Keywords = "ui, widget, create, screen, viewport"))
class FLOWGRAPHEXTENDED_API UFlowNode_CreateWidget : public UFlowNode
{
	GENERATED_UCLASS_BODY()

public:
	/** The UserWidget class to create and display */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget")
	TSubclassOf<UUserWidget> WidgetClass;

	/** Z-Order for the widget (higher values appear on top) */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget", meta = (ClampMin = "0"))
	int32 ZOrder = 0;

	/** Whether to remove the widget when this node is cleaned up */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget")
	bool bRemoveOnCleanup = true;

	/** Player index to add the widget to (0 = first player) */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget", meta = (ClampMin = "0"))
	int32 PlayerIndex = 0;

protected:
	// UFlowNode interface
	virtual void ExecuteInput(const FName& PinName) override;
	virtual void Cleanup() override;

#if WITH_EDITOR
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
	virtual FText GetNodeTitle() const override;
	virtual FText GetNodeToolTip() const override;
	virtual EDataValidationResult ValidateNode() override;
#endif

public:
	/** Get the created widget instance */
	UFUNCTION(BlueprintPure, Category = "Widget")
	UUserWidget* GetCreatedWidget() const { return CreatedWidget; }

	/** Manually remove the created widget from the viewport */
	UFUNCTION(BlueprintCallable, Category = "Widget")
	void RemoveWidget();

protected:
	/** Reference to the created widget instance */
	UPROPERTY(Transient)
	TObjectPtr<UUserWidget> CreatedWidget;

	/** Cached reference to the player controller */
	UPROPERTY(Transient)
	TObjectPtr<class APlayerController> CachedPlayerController;

	/** Helper function to get the player controller for the specified player index */
	APlayerController* GetPlayerController() const;

	/** Helper function to create and add the widget to viewport */
	bool CreateAndAddWidget();

	/** Helper function to validate the widget class */
	bool IsWidgetClassValid() const;
};
