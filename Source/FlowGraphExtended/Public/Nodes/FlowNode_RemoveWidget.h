#pragma once

#include "CoreMinimal.h"
#include "Nodes/FlowNode.h"
#include "Blueprint/UserWidget.h"
#include "FlowNode_RemoveWidget.generated.h"

/**
 * FlowNode that removes a UserWidget from the player's screen
 * 
 * This node can remove widgets by class type or by specific widget reference.
 * Useful for cleaning up UI elements created by FlowNode_CreateWidget.
 */
UCLASS(NotBlueprintable, meta = (DisplayName = "Remove Widget", Keywords = "ui, widget, remove, destroy, screen, viewport"))
class FLOWGRAPHEXTENDED_API UFlowNode_RemoveWidget : public UFlowNode
{
	GENERATED_UCLASS_BODY()

public:
	/** How to identify which widget(s) to remove */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget")
	enum class ERemovalMode : uint8
	{
		/** Remove all widgets of the specified class */
		ByClass,
		/** Remove a specific widget instance */
		ByReference,
		/** Remove all widgets from the viewport */
		All
	} RemovalMode = ERemovalMode::ByClass;

	/** The UserWidget class to remove (used when RemovalMode is ByClass) */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget", 
		meta = (EditCondition = "RemovalMode == ERemovalMode::ByClass", EditConditionHides))
	TSubclassOf<UUserWidget> WidgetClass;

	/** Specific widget instance to remove (used when RemovalMode is ByReference) */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget",
		meta = (EditCondition = "RemovalMode == ERemovalMode::ByReference", EditConditionHides))
	TObjectPtr<UUserWidget> WidgetInstance;

	/** Player index to remove widgets from (0 = first player) */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Widget", meta = (ClampMin = "0"))
	int32 PlayerIndex = 0;

protected:
	// UFlowNode interface
	virtual void ExecuteInput(const FName& PinName) override;

#if WITH_EDITOR
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
	virtual FText GetNodeTitle() const override;
	virtual FText GetNodeToolTip() const override;
	virtual EDataValidationResult ValidateNode() override;
#endif

protected:
	/** Helper function to get the player controller for the specified player index */
	APlayerController* GetPlayerController() const;

	/** Remove widgets by class type */
	int32 RemoveWidgetsByClass();

	/** Remove a specific widget instance */
	bool RemoveWidgetByReference();

	/** Remove all widgets from viewport */
	int32 RemoveAllWidgets();

	/** Get all widgets of a specific class from the viewport */
	TArray<UUserWidget*> GetWidgetsOfClass(TSubclassOf<UUserWidget> InWidgetClass) const;
};
