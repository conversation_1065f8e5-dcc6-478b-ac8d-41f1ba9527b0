#include "FlowGraphExtended.h"

DEFINE_LOG_CATEGORY(LogFlowGraphExtended);

#define LOCTEXT_NAMESPACE "FFlowGraphExtendedModule"

void FFlowGraphExtendedModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uproject file per-module
	UE_LOG(LogFlowGraphExtended, Log, TEXT("FlowGraphExtended module started"));
}

void FFlowGraphExtendedModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module. For modules that support dynamic reloading,
	// we call this function before unloading the module.
	UE_LOG(LogFlowGraphExtended, Log, TEXT("FlowGraphExtended module shutdown"));
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FFlowGraphExtendedModule, FlowGraphExtended)
