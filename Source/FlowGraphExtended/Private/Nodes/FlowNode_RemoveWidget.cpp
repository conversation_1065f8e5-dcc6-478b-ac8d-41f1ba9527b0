#include "Nodes/FlowNode_RemoveWidget.h"
#include "FlowGraphExtended.h"
#include "Blueprint/UserWidget.h"
#include "Engine/World.h"
#include "Engine/GameViewportClient.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Blueprint/WidgetBlueprintLibrary.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(FlowNode_RemoveWidget)

#define LOCTEXT_NAMESPACE "FlowNode_RemoveWidget"

UFlowNode_RemoveWidget::UFlowNode_RemoveWidget(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	// Set up default pins
	InputPins = { FFlowPin(TEXT("In")) };
	OutputPins = { 
		FFlowPin(TEXT("Success")), 
		FFlowPin(TEXT("Failed")) 
	};
	
	// Default values
	RemovalMode = ERemovalMode::ByClass;
	PlayerIndex = 0;
	WidgetClass = nullptr;
	WidgetInstance = nullptr;

#if WITH_EDITOR
	NodeDisplayStyle = EFlowNodeStyle::Default;
#endif
}

void UFlowNode_RemoveWidget::ExecuteInput(const FName& PinName)
{
	if (PinName != TEXT("In"))
	{
		return;
	}

	bool bSuccess = false;
	int32 RemovedCount = 0;

	switch (RemovalMode)
	{
		case ERemovalMode::ByClass:
		{
			if (!WidgetClass)
			{
				LogVisualNovel(TEXT("Widget class is not set for ByClass removal mode"), ELogVerbosity::Error);
				TriggerOutput(TEXT("Failed"), true);
				return;
			}
			
			RemovedCount = RemoveWidgetsByClass();
			bSuccess = RemovedCount > 0;
			LogVisualNovel(FString::Printf(TEXT("Removed %d widgets of class %s"), 
				RemovedCount, *WidgetClass->GetName()));
			break;
		}
		
		case ERemovalMode::ByReference:
		{
			if (!WidgetInstance)
			{
				LogVisualNovel(TEXT("Widget instance is not set for ByReference removal mode"), ELogVerbosity::Error);
				TriggerOutput(TEXT("Failed"), true);
				return;
			}
			
			bSuccess = RemoveWidgetByReference();
			LogVisualNovel(FString::Printf(TEXT("Removed widget instance: %s"), 
				bSuccess ? TEXT("Success") : TEXT("Failed")));
			break;
		}
		
		case ERemovalMode::All:
		{
			RemovedCount = RemoveAllWidgets();
			bSuccess = RemovedCount >= 0; // Always succeed for "remove all"
			LogVisualNovel(FString::Printf(TEXT("Removed all widgets from viewport (%d total)"), RemovedCount));
			break;
		}
	}

	TriggerOutput(bSuccess ? TEXT("Success") : TEXT("Failed"), true);
}

APlayerController* UFlowNode_RemoveWidget::GetPlayerController() const
{
	if (UWorld* World = GetWorld())
	{
		return UGameplayStatics::GetPlayerController(World, PlayerIndex);
	}
	
	return nullptr;
}

int32 UFlowNode_RemoveWidget::RemoveWidgetsByClass()
{
	TArray<UUserWidget*> WidgetsToRemove = GetWidgetsOfClass(WidgetClass);
	
	for (UUserWidget* Widget : WidgetsToRemove)
	{
		if (Widget && IsValid(Widget))
		{
			Widget->RemoveFromParent();
		}
	}
	
	return WidgetsToRemove.Num();
}

bool UFlowNode_RemoveWidget::RemoveWidgetByReference()
{
	if (WidgetInstance && IsValid(WidgetInstance))
	{
		WidgetInstance->RemoveFromParent();
		return true;
	}
	
	return false;
}

int32 UFlowNode_RemoveWidget::RemoveAllWidgets()
{
	APlayerController* PlayerController = GetPlayerController();
	if (!PlayerController)
	{
		LogVisualNovel(FString::Printf(TEXT("Could not find player controller for player index %d"), PlayerIndex), ELogVerbosity::Error);
		return -1;
	}

	// Get all widgets from the viewport
	TArray<UUserWidget*> AllWidgets;
	UWidgetBlueprintLibrary::GetAllWidgetsOfClass(GetWorld(), AllWidgets, UUserWidget::StaticClass());
	
	int32 RemovedCount = 0;
	for (UUserWidget* Widget : AllWidgets)
	{
		if (Widget && IsValid(Widget) && Widget->IsInViewport())
		{
			Widget->RemoveFromParent();
			RemovedCount++;
		}
	}
	
	return RemovedCount;
}

TArray<UUserWidget*> UFlowNode_RemoveWidget::GetWidgetsOfClass(TSubclassOf<UUserWidget> InWidgetClass) const
{
	TArray<UUserWidget*> FoundWidgets;
	
	if (InWidgetClass)
	{
		UWidgetBlueprintLibrary::GetAllWidgetsOfClass(GetWorld(), FoundWidgets, InWidgetClass);
	}
	
	return FoundWidgets;
}

#if WITH_EDITOR
void UFlowNode_RemoveWidget::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property)
	{
		const FName PropertyName = PropertyChangedEvent.Property->GetFName();
		
		// Validate widget class when it changes
		if (PropertyName == GET_MEMBER_NAME_CHECKED(UFlowNode_RemoveWidget, WidgetClass))
		{
			if (WidgetClass && !WidgetClass->IsChildOf(UUserWidget::StaticClass()))
			{
				LogVisualNovel(TEXT("Widget class must be a subclass of UUserWidget"), ELogVerbosity::Error);
				WidgetClass = nullptr;
			}
		}
	}
}

FText UFlowNode_RemoveWidget::GetNodeTitle() const
{
	switch (RemovalMode)
	{
		case ERemovalMode::ByClass:
			if (WidgetClass)
			{
				return FText::Format(LOCTEXT("NodeTitleByClass", "Remove Widget: {0}"), 
					FText::FromString(WidgetClass->GetName()));
			}
			return LOCTEXT("NodeTitleByClassEmpty", "Remove Widget: (No Class)");
			
		case ERemovalMode::ByReference:
			return LOCTEXT("NodeTitleByReference", "Remove Widget: By Reference");
			
		case ERemovalMode::All:
			return LOCTEXT("NodeTitleAll", "Remove All Widgets");
			
		default:
			return LOCTEXT("NodeTitle", "Remove Widget");
	}
}

FText UFlowNode_RemoveWidget::GetNodeToolTip() const
{
	return LOCTEXT("NodeTooltip", "Removes UserWidget instances from the player's viewport. Can remove by class, by reference, or remove all widgets.");
}

EDataValidationResult UFlowNode_RemoveWidget::ValidateNode()
{
	EDataValidationResult Result = Super::ValidateNode();
	
	if (RemovalMode == ERemovalMode::ByClass && !WidgetClass)
	{
		LogVisualNovel(TEXT("Widget class must be set when using ByClass removal mode"), ELogVerbosity::Error);
		Result = EDataValidationResult::Invalid;
	}
	
	if (RemovalMode == ERemovalMode::ByReference && !WidgetInstance)
	{
		LogVisualNovel(TEXT("Widget instance must be set when using ByReference removal mode"), ELogVerbosity::Warning);
		// This is a warning, not an error, since the widget might be set at runtime
	}
	
	if (PlayerIndex < 0)
	{
		LogVisualNovel(TEXT("Player index must be 0 or greater"), ELogVerbosity::Error);
		Result = EDataValidationResult::Invalid;
	}
	
	return Result;
}
#endif

#undef LOCTEXT_NAMESPACE
