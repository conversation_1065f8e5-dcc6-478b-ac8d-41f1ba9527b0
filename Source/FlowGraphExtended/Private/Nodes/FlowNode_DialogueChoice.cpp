#include "FlowGraphExtended/Public/Nodes/FlowNode_DialogueChoice.h"
#include "Nodes/FlowNode_DialogueChoice.h"
#include "FlowGraphExtended.h"
#include "GameplayTagsManager.h"
#include "Engine/World.h"
#include "TimerManager.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(FlowNode_DialogueChoice)

#define LOCTEXT_NAMESPACE "FlowNode_DialogueChoice"

UFlowNode_DialogueChoice::UFlowNode_DialogueChoice(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	Category = TEXT("Visual Novel");
	
	// Set up default input pin
	InputPins = { FFlowPin(TEXT("In")) };
	
	// Initialize with two default choices
	Choices.SetNum(2);
	Choices[0].ChoiceText = LOCTEXT("DefaultChoice1", "Choice 1");
	Choices[1].ChoiceText = LOCTEXT("DefaultChoice2", "Choice 2");
	
	// Set default values
	ChoiceTimeLimit = 0.0f;
	DefaultChoiceIndex = -1;
	bChoiceSelected = false;
}
	
void UFlowNode_DialogueChoice::ExecuteInput(const FName& PinName)
{
	if (PinName != TEXT("In"))
	{
		return;
	}

	// Reset choice selection state
	bChoiceSelected = false;

	// Update choice availability based on current gameplay tags
	UpdateChoiceAvailability();

	// Get available choices
	TArray<FDialogueChoiceData> AvailableChoices = GetAvailableChoices();
	
	if (AvailableChoices.Num() == 0)
	{
		UE_LOG(LogFlowGraphExtended, Warning, TEXT("FlowNode_DialogueChoice: No available choices, finishing node"));
		Finish();
		return;
	}

	// If only one choice is available, auto-select it
	if (AvailableChoices.Num() == 1)
	{
		for (int32 i = 0; i < Choices.Num(); ++i)
		{
			if (Choices[i].bIsAvailable)
			{
				OnChoiceSelected(i);
				return;
			}
		}
	}

	// Set up timeout if specified
	if (ChoiceTimeLimit > 0.0f)
	{
		if (UWorld* World = GetWorld())
		{
			World->GetTimerManager().SetTimer(
				ChoiceTimeoutHandle,
				this,
				&UFlowNode_DialogueChoice::OnChoiceTimeout,
				ChoiceTimeLimit,
				false
			);
		}
	}

	// TODO: Trigger UI to display choices
	// This would typically involve calling a delegate or event that the UI system listens to
	UE_LOG(LogFlowGraphExtended, Log, TEXT("FlowNode_DialogueChoice: Presenting %d choices to player"), AvailableChoices.Num());
}

void UFlowNode_DialogueChoice::Cleanup()
{
	// Clear any active timers
	if (UWorld* World = GetWorld())
	{
		World->GetTimerManager().ClearTimer(ChoiceTimeoutHandle);
	}

	bChoiceSelected = false;
	
	Super::Cleanup();
}

void UFlowNode_DialogueChoice::OnChoiceSelected(int32 ChoiceIndex)
{
	// Prevent multiple selections
	if (bChoiceSelected)
	{
		return;
	}

	// Validate choice index
	if (!Choices.IsValidIndex(ChoiceIndex))
	{
		UE_LOG(LogFlowGraphExtended, Error, TEXT("FlowNode_DialogueChoice: Invalid choice index %d"), ChoiceIndex);
		return;
	}

	// Check if choice is available
	if (!IsChoiceAvailable(ChoiceIndex))
	{
		UE_LOG(LogFlowGraphExtended, Warning, TEXT("FlowNode_DialogueChoice: Choice %d is not available"), ChoiceIndex);
		return;
	}

	bChoiceSelected = true;

	// Clear timeout timer
	if (UWorld* World = GetWorld())
	{
		World->GetTimerManager().ClearTimer(ChoiceTimeoutHandle);
	}

	// Apply tags from selected choice
	const FDialogueChoiceData& SelectedChoice = Choices[ChoiceIndex];
	if (SelectedChoice.TagsToAdd.Num() > 0)
	{
		// TODO: Apply tags to appropriate gameplay tag container
		// This would typically involve a game-specific tag management system
		UE_LOG(LogFlowGraphExtended, Log, TEXT("FlowNode_DialogueChoice: Would apply %d tags from choice %d"), 
			SelectedChoice.TagsToAdd.Num(), ChoiceIndex);
	}

	// Trigger output pin corresponding to the choice
	FString OutputPinName = FString::Printf(TEXT("Choice %d"), ChoiceIndex + 1);
	TriggerOutput(*OutputPinName, true);
}

TArray<FDialogueChoiceData> UFlowNode_DialogueChoice::GetAvailableChoices() const
{
	TArray<FDialogueChoiceData> AvailableChoices;
	
	for (const FDialogueChoiceData& Choice : Choices)
	{
		if (Choice.bIsAvailable)
		{
			AvailableChoices.Add(Choice);
		}
	}
	
	return AvailableChoices;
}

bool UFlowNode_DialogueChoice::IsChoiceAvailable(int32 ChoiceIndex) const
{
	if (!Choices.IsValidIndex(ChoiceIndex))
	{
		return false;
	}

	return Choices[ChoiceIndex].bIsAvailable;
}

void UFlowNode_DialogueChoice::UpdateChoiceAvailability()
{
	// TODO: Implement gameplay tag checking logic
	// This would typically check against a player's current gameplay tag container
	
	for (FDialogueChoiceData& Choice : Choices)
	{
		// For now, assume all choices are available if they have no required tags
		// or implement your specific tag checking logic here
		Choice.bIsAvailable = Choice.RequiredTags.Num() == 0;
		
		// Example tag checking (would need access to player's tag container):
		// Choice.bIsAvailable = PlayerTagContainer.HasAll(Choice.RequiredTags);
	}
}

void UFlowNode_DialogueChoice::OnChoiceTimeout()
{
	if (bChoiceSelected)
	{
		return;
	}

	UE_LOG(LogFlowGraphExtended, Log, TEXT("FlowNode_DialogueChoice: Choice timeout reached"));

	// Auto-select default choice if specified and valid
	if (Choices.IsValidIndex(DefaultChoiceIndex) && IsChoiceAvailable(DefaultChoiceIndex))
	{
		OnChoiceSelected(DefaultChoiceIndex);
	}
	else
	{
		// No valid default choice, just finish the node
		Finish();
	}
}

#if WITH_EDITOR
void UFlowNode_DialogueChoice::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property)
	{
		const FName PropertyName = PropertyChangedEvent.Property->GetFName();
		
		// Update output pins when choices change
		if (PropertyName == GET_MEMBER_NAME_CHECKED(UFlowNode_DialogueChoice, Choices))
		{
			// Regenerate output pins based on number of choices
			OutputPins.Empty();
			for (int32 i = 0; i < Choices.Num(); ++i)
			{
				FString PinName = FString::Printf(TEXT("Choice %d"), i + 1);
				OutputPins.Add(FFlowPin(*PinName));
			}
			
			OnReconstructionRequested.ExecuteIfBound();
		}
	}
}

FText UFlowNode_DialogueChoice::GetNodeTitle() const
{
	return LOCTEXT("NodeTitle", "Dialogue Choice");
}

FText UFlowNode_DialogueChoice::GetNodeToolTip() const
{
	return LOCTEXT("NodeTooltip", "Presents dialogue choices to the player and branches execution based on selection. Supports conditional availability and timeout.");
}
#endif

#undef LOCTEXT_NAMESPACE
